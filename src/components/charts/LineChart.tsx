"use client";

import React from "react";
import { Line<PERSON><PERSON>, Line, XAxis, YAxis, ResponsiveContainer } from "recharts";
import { TimeSeriesDataPoint } from "@/types/dashboard";

interface LineChartComponentProps {
  data: TimeSeriesDataPoint[];
  title: string;
  subtitle?: string;
  color?: string;
  yAxisLabel?: string;
}

export const LineChartComponent: React.FC<LineChartComponentProps> = React.memo(
  ({ data, title, subtitle, color = "#3B82F6", yAxisLabel }) => {
    // Format data for recharts
    const chartData = React.useMemo(
      () =>
        data.map((point, index) => ({
          ...point,
          index,
        })),
      [data]
    );

    return (
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="mb-4">
          <h3 className="text-white text-lg font-semibold">{title}</h3>
          {subtitle && <p className="text-gray-400 text-sm">{subtitle}</p>}
        </div>

        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <XAxis
                dataKey="index"
                axisLine={false}
                tickLine={false}
                tick={false}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: "#9CA3AF", fontSize: 12 }}
                label={
                  yAxisLabel
                    ? {
                        value: yAxisLabel,
                        angle: -90,
                        position: "insideLeft",
                        style: { textAnchor: "middle", fill: "#9CA3AF" },
                      }
                    : undefined
                }
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={color}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: color }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  }
);

LineChartComponent.displayName = "LineChartComponent";
