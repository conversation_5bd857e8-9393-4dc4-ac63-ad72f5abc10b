import React from "react";
import { TrendingUp, TrendingDown } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: "up" | "down";
  trendValue?: string;
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = React.memo(
  ({ title, value, subtitle, trend, trendValue, className = "" }) => {
    const formattedValue = React.useMemo(
      () => (typeof value === "number" ? value.toLocaleString() : value),
      [value]
    );

    return (
      <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-gray-400 text-sm font-medium uppercase tracking-wide">
            {title}
          </h3>
          {trend && trendValue && (
            <div
              className={`flex items-center text-sm ${
                trend === "up" ? "text-red-400" : "text-green-400"
              }`}
            >
              {trend === "up" ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              {trendValue}
            </div>
          )}
        </div>

        <div className="mb-1">
          <span className="text-3xl font-bold text-white">
            {formattedValue}
          </span>
        </div>

        <p className="text-gray-400 text-sm">{subtitle}</p>
      </div>
    );
  }
);

MetricCard.displayName = "MetricCard";
