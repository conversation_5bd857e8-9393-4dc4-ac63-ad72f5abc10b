{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nexport const Header: React.FC = () => {\n  const scrollingText = \"*** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS *** FASTER DOCKER BUILDS ***\";\n\n  return (\n    <div className=\"bg-blue-600 text-white py-2 overflow-hidden relative\">\n      <div className=\"animate-scroll whitespace-nowrap\">\n        <span className=\"text-sm font-medium\">\n          {scrollingText}\n        </span>\n      </div>\n      \n      <style jsx>{`\n        @keyframes scroll {\n          0% {\n            transform: translateX(100%);\n          }\n          100% {\n            transform: translateX(-100%);\n          }\n        }\n        \n        .animate-scroll {\n          animation: scroll 30s linear infinite;\n        }\n      `}</style>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;;AAIO,MAAM,SAAmB;IAC9B,MAAM,gBAAgB;IAEtB,qBACE,8OAAC;iDAAc;;0BACb,8OAAC;yDAAc;0BACb,cAAA,8OAAC;6DAAe;8BACb;;;;;;;;;;;;;;;;;;;;;AAoBX", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { NavItem } from \"@/types/dashboard\";\nimport {\n  BarChart3,\n  Settings,\n  MessageSquare,\n  GitBranch,\n  Container,\n  Database,\n  Shield,\n  CreditCard,\n} from \"lucide-react\";\n\ninterface SidebarProps {\n  navigationItems: NavItem[];\n}\n\nconst getIcon = (id: string) => {\n  const iconMap: Record<string, React.ReactNode> = {\n    feedback: <MessageSquare className=\"w-4 h-4\" />,\n    \"actions-analytics\": <BarChart3 className=\"w-4 h-4\" />,\n    \"workflow-runs\": <GitBranch className=\"w-4 h-4\" />,\n    \"docker-builds\": <Container className=\"w-4 h-4\" />,\n    cache: <Database className=\"w-4 h-4\" />,\n    \"study-risks\": <Shield className=\"w-4 h-4\" />,\n    \"usage-billing\": <CreditCard className=\"w-4 h-4\" />,\n    settings: <Settings className=\"w-4 h-4\" />,\n  };\n\n  return iconMap[id] || <BarChart3 className=\"w-4 h-4\" />;\n};\n\nexport const Sidebar: React.FC<SidebarProps> = ({ navigationItems }) => {\n  return (\n    <div className=\"bg-gray-900 w-64 min-h-screen p-4\">\n      {/* Logo */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-white rounded flex items-center justify-center\">\n            <span className=\"text-black font-bold text-sm\">B</span>\n          </div>\n          <span className=\"text-white font-semibold\">blacksmith</span>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"space-y-1\">\n        {navigationItems.map((item) => (\n          <div key={item.id}>\n            <button\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                item.active\n                  ? \"bg-blue-600 text-white\"\n                  : \"text-gray-300 hover:bg-gray-800 hover:text-white\"\n              }`}\n            >\n              {getIcon(item.id)}\n              <span>{item.label}</span>\n            </button>\n          </div>\n        ))}\n      </nav>\n\n      {/* Bottom section */}\n      <div className=\"absolute bottom-4 left-4 right-4\">\n        <div className=\"bg-gray-800 rounded-lg p-3\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <div className=\"w-2 h-2 bg-orange-400 rounded-full\"></div>\n            <span className=\"text-orange-400 text-xs font-medium\">\n              STORAGE PERFORMANCE\n            </span>\n          </div>\n          <div className=\"text-white text-sm\">\n            <div>CPU Usage</div>\n            <div className=\"text-xs text-gray-400\">85%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;AAmBA,MAAM,UAAU,CAAC;IACf,MAAM,UAA2C;QAC/C,wBAAU,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACnC,mCAAqB,8OAAC,kNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC1C,+BAAiB,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACtC,+BAAiB,8OAAC,4MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACtC,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC3B,6BAAe,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACjC,+BAAiB,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QACvC,wBAAU,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAChC;IAEA,OAAO,OAAO,CAAC,GAAG,kBAAI,8OAAC,kNAAA,CAAA,YAAS;QAAC,WAAU;;;;;;AAC7C;AAEO,MAAM,UAAkC,CAAC,EAAE,eAAe,EAAE;IACjE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAK,WAAU;sCAA2B;;;;;;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;kCACC,cAAA,8OAAC;4BACC,WAAW,CAAC,8FAA8F,EACxG,KAAK,MAAM,GACP,2BACA,oDACJ;;gCAED,QAAQ,KAAK,EAAE;8CAChB,8OAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;uBATX,KAAK,EAAE;;;;;;;;;;0BAgBrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAI;;;;;;8CACL,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnD", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/ui/MetricCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { TrendingUp, TrendingDown } from 'lucide-react';\n\ninterface MetricCardProps {\n  title: string;\n  value: string | number;\n  subtitle: string;\n  trend?: 'up' | 'down';\n  trendValue?: string;\n  className?: string;\n}\n\nexport const MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  value,\n  subtitle,\n  trend,\n  trendValue,\n  className = ''\n}) => {\n  return (\n    <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>\n      <div className=\"flex items-center justify-between mb-2\">\n        <h3 className=\"text-gray-400 text-sm font-medium uppercase tracking-wide\">\n          {title}\n        </h3>\n        {trend && trendValue && (\n          <div className={`flex items-center text-sm ${\n            trend === 'up' ? 'text-red-400' : 'text-green-400'\n          }`}>\n            {trend === 'up' ? (\n              <TrendingUp className=\"w-4 h-4 mr-1\" />\n            ) : (\n              <TrendingDown className=\"w-4 h-4 mr-1\" />\n            )}\n            {trendValue}\n          </div>\n        )}\n      </div>\n      \n      <div className=\"mb-1\">\n        <span className=\"text-3xl font-bold text-white\">\n          {typeof value === 'number' ? value.toLocaleString() : value}\n        </span>\n      </div>\n      \n      <p className=\"text-gray-400 text-sm\">\n        {subtitle}\n      </p>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAWO,MAAM,aAAwC,CAAC,EACpD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,KAAK,EACL,UAAU,EACV,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;;0BACvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,SAAS,4BACR,8OAAC;wBAAI,WAAW,CAAC,0BAA0B,EACzC,UAAU,OAAO,iBAAiB,kBAClC;;4BACC,UAAU,qBACT,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;qDAEtB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAEzB;;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BACb,OAAO,UAAU,WAAW,MAAM,cAAc,KAAK;;;;;;;;;;;0BAI1D,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/charts/BarChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axi<PERSON>, <PERSON>A<PERSON><PERSON>, ResponsiveContainer } from 'recharts';\nimport { JobDurationDistribution } from '@/types/dashboard';\n\ninterface BarChartComponentProps {\n  data: JobDurationDistribution[];\n  title: string;\n  subtitle?: string;\n}\n\nexport const BarChartComponent: React.FC<BarChartComponentProps> = ({\n  data,\n  title,\n  subtitle\n}) => {\n  return (\n    <div className=\"bg-gray-800 rounded-lg p-6\">\n      <div className=\"mb-4\">\n        <h3 className=\"text-white text-lg font-semibold\">{title}</h3>\n        {subtitle && (\n          <p className=\"text-gray-400 text-sm\">{subtitle}</p>\n        )}\n      </div>\n      \n      <div className=\"h-64\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n            <XAxis \n              dataKey=\"duration\" \n              axisLine={false}\n              tickLine={false}\n              tick={{ fill: '#9CA3AF', fontSize: 12 }}\n            />\n            <YAxis \n              axisLine={false}\n              tickLine={false}\n              tick={{ fill: '#9CA3AF', fontSize: 12 }}\n            />\n            <Bar \n              dataKey=\"count\" \n              fill=\"#3B82F6\"\n              radius={[2, 2, 0, 0]}\n            />\n          </BarChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAYO,MAAM,oBAAsD,CAAC,EAClE,IAAI,EACJ,KAAK,EACL,QAAQ,EACT;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAM,QAAQ;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAI,QAAQ;wBAAE;;0CACtE,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM;oCAAW,UAAU;gCAAG;;;;;;0CAExC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM;oCAAW,UAAU;gCAAG;;;;;;0CAExC,8OAAC,mJAAA,CAAA,MAAG;gCACF,SAAQ;gCACR,MAAK;gCACL,QAAQ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/charts/LineChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';\nimport { TimeSeriesDataPoint } from '@/types/dashboard';\n\ninterface LineChartComponentProps {\n  data: TimeSeriesDataPoint[];\n  title: string;\n  subtitle?: string;\n  color?: string;\n  yAxisLabel?: string;\n}\n\nexport const LineChartComponent: React.FC<LineChartComponentProps> = ({\n  data,\n  title,\n  subtitle,\n  color = '#3B82F6',\n  yAxisLabel\n}) => {\n  // Format data for recharts\n  const chartData = data.map((point, index) => ({\n    ...point,\n    index\n  }));\n\n  return (\n    <div className=\"bg-gray-800 rounded-lg p-6\">\n      <div className=\"mb-4\">\n        <h3 className=\"text-white text-lg font-semibold\">{title}</h3>\n        {subtitle && (\n          <p className=\"text-gray-400 text-sm\">{subtitle}</p>\n        )}\n      </div>\n      \n      <div className=\"h-48\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n            <XAxis \n              dataKey=\"index\"\n              axisLine={false}\n              tickLine={false}\n              tick={false}\n            />\n            <YAxis \n              axisLine={false}\n              tickLine={false}\n              tick={{ fill: '#9CA3AF', fontSize: 12 }}\n              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#9CA3AF' } } : undefined}\n            />\n            <Line \n              type=\"monotone\" \n              dataKey=\"value\" \n              stroke={color}\n              strokeWidth={2}\n              dot={false}\n              activeDot={{ r: 4, fill: color }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAcO,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,SAAS,EACjB,UAAU,EACX;IACC,2BAA2B;IAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC5C,GAAG,KAAK;YACR;QACF,CAAC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;oBACjD,0BACC,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;wBAAW,QAAQ;4BAAE,KAAK;4BAAG,OAAO;4BAAI,MAAM;4BAAI,QAAQ;wBAAE;;0CAC3E,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,MAAM;;;;;;0CAER,8OAAC,qJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,MAAM;oCAAE,MAAM;oCAAW,UAAU;gCAAG;gCACtC,OAAO,aAAa;oCAAE,OAAO;oCAAY,OAAO,CAAC;oCAAI,UAAU;oCAAc,OAAO;wCAAE,YAAY;wCAAU,MAAM;oCAAU;gCAAE,IAAI;;;;;;0CAEpI,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAQ;gCACR,aAAa;gCACb,KAAK;gCACL,WAAW;oCAAE,GAAG;oCAAG,MAAM;gCAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/ui/PerformanceTable.tsx"], "sourcesContent": ["import React from 'react';\nimport { PerformanceMetric } from '@/types/dashboard';\n\ninterface PerformanceTableProps {\n  metrics: PerformanceMetric[];\n  title: string;\n}\n\nexport const PerformanceTable: React.FC<PerformanceTableProps> = ({\n  metrics,\n  title\n}) => {\n  return (\n    <div className=\"bg-gray-800 rounded-lg p-6\">\n      <h3 className=\"text-white text-lg font-semibold mb-4\">{title}</h3>\n      \n      <div className=\"space-y-3\">\n        <div className=\"grid grid-cols-4 gap-4 text-gray-400 text-sm font-medium border-b border-gray-700 pb-2\">\n          <span>RESOURCE/METRIC</span>\n          <span>LOG</span>\n          <span>POPULAR</span>\n          <span>SLOW</span>\n          <span>VERY SLOW</span>\n        </div>\n        \n        {metrics.map((metric, index) => (\n          <div key={index} className=\"grid grid-cols-4 gap-4 text-sm\">\n            <span className=\"text-white font-medium\">{metric.name}</span>\n            <span className=\"text-gray-300\">{metric.value}</span>\n            <span className=\"text-gray-300\">{metric.percentage}</span>\n            <span className=\"text-gray-300\">{metric.percentage}</span>\n            <span className=\"text-gray-300\">{metric.percentage}</span>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAQO,MAAM,mBAAoD,CAAC,EAChE,OAAO,EACP,KAAK,EACN;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAyC;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;oBAGP,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAK,WAAU;8CAA0B,OAAO,IAAI;;;;;;8CACrD,8OAAC;oCAAK,WAAU;8CAAiB,OAAO,KAAK;;;;;;8CAC7C,8OAAC;oCAAK,WAAU;8CAAiB,OAAO,UAAU;;;;;;8CAClD,8OAAC;oCAAK,WAAU;8CAAiB,OAAO,UAAU;;;;;;8CAClD,8OAAC;oCAAK,WAAU;8CAAiB,OAAO,UAAU;;;;;;;2BAL1C;;;;;;;;;;;;;;;;;AAWpB", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Header } from './layout/Header';\nimport { Sidebar } from './layout/Sidebar';\nimport { MetricCard } from './ui/MetricCard';\nimport { BarChartComponent } from './charts/BarChart';\nimport { LineChartComponent } from './charts/LineChart';\nimport { PerformanceTable } from './ui/PerformanceTable';\nimport { DashboardData, NavItem } from '@/types/dashboard';\n\ninterface DashboardProps {\n  data: DashboardData;\n  navigationItems: NavItem[];\n}\n\nexport const Dashboard: React.FC<DashboardProps> = ({ data, navigationItems }) => {\n  const { metrics, jobDurationDistribution, charts, performanceMetrics } = data;\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      <Header />\n      \n      <div className=\"flex\">\n        <Sidebar navigationItems={navigationItems} />\n        \n        <main className=\"flex-1 p-6\">\n          {/* Top Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n            <MetricCard\n              title=\"Total Jobs\"\n              value={metrics.totalJobs}\n              subtitle=\"TOTAL JOBS\"\n              trend=\"up\"\n              trendValue=\"+12.5%\"\n            />\n            <MetricCard\n              title=\"Avg Job Duration\"\n              value={`${metrics.avgJobDuration}s`}\n              subtitle=\"AVG JOB DURATION\"\n              trend=\"down\"\n              trendValue=\"-8.3%\"\n            />\n            <MetricCard\n              title=\"Failure Rate\"\n              value={`${metrics.failureRate}%`}\n              subtitle=\"FAILURE RATE\"\n              trend=\"up\"\n              trendValue=\"+2.1%\"\n            />\n          </div>\n\n          {/* Charts Section */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\">\n            {/* Job Duration Distribution */}\n            <div className=\"lg:col-span-2\">\n              <BarChartComponent\n                data={jobDurationDistribution}\n                title=\"Job Duration Distribution\"\n                subtitle=\"Distribution of job execution times\"\n              />\n            </div>\n            \n            {/* Performance Table */}\n            <div>\n              <PerformanceTable\n                metrics={performanceMetrics}\n                title=\"Performance Metrics\"\n              />\n            </div>\n          </div>\n\n          {/* Time Series Charts */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <LineChartComponent\n              data={charts.jobsRuns}\n              title=\"Jobs Runs\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#3B82F6\"\n            />\n            <LineChartComponent\n              data={charts.failureWave}\n              title=\"Failure Wave\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#DC2626\"\n            />\n            <LineChartComponent\n              data={charts.p95Duration}\n              title=\"P95 Duration\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#F59E0B\"\n            />\n            <LineChartComponent\n              data={charts.p99Duration}\n              title=\"P99 Duration\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#8B5CF6\"\n            />\n            <LineChartComponent\n              data={charts.storagePerformance}\n              title=\"Storage Performance\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#10B981\"\n            />\n            <LineChartComponent\n              data={charts.outputPerformance}\n              title=\"Output Performance\"\n              subtitle=\"Total minutes (anomalies)\"\n              color=\"#06B6D4\"\n            />\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAgBO,MAAM,YAAsC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE;IAC3E,MAAM,EAAE,OAAO,EAAE,uBAAuB,EAAE,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAEzE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAO;wBAAC,iBAAiB;;;;;;kCAE1B,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sIAAA,CAAA,aAAU;wCACT,OAAM;wCACN,OAAO,QAAQ,SAAS;wCACxB,UAAS;wCACT,OAAM;wCACN,YAAW;;;;;;kDAEb,8OAAC,sIAAA,CAAA,aAAU;wCACT,OAAM;wCACN,OAAO,GAAG,QAAQ,cAAc,CAAC,CAAC,CAAC;wCACnC,UAAS;wCACT,OAAM;wCACN,YAAW;;;;;;kDAEb,8OAAC,sIAAA,CAAA,aAAU;wCACT,OAAM;wCACN,OAAO,GAAG,QAAQ,WAAW,CAAC,CAAC,CAAC;wCAChC,UAAS;wCACT,OAAM;wCACN,YAAW;;;;;;;;;;;;0CAKf,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAA,CAAA,oBAAiB;4CAChB,MAAM;4CACN,OAAM;4CACN,UAAS;;;;;;;;;;;kDAKb,8OAAC;kDACC,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,OAAM;;;;;;;;;;;;;;;;;0CAMZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,QAAQ;wCACrB,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;kDAER,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,WAAW;wCACxB,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;kDAER,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,WAAW;wCACxB,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;kDAER,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,WAAW;wCACxB,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;kDAER,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,kBAAkB;wCAC/B,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;kDAER,8OAAC,yIAAA,CAAA,qBAAkB;wCACjB,MAAM,OAAO,iBAAiB;wCAC9B,OAAM;wCACN,UAAS;wCACT,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}