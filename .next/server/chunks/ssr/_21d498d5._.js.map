{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/Dashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Dashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dashboard() from the server but Dashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Dashboard.tsx <module evaluation>\",\n    \"Dashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/components/Dashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Dashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dashboard() from the server but Dashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Dashboard.tsx\",\n    \"Dashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/data/mockData.ts"], "sourcesContent": ["import { DashboardData, NavItem } from '@/types/dashboard';\n\n// Generate realistic time series data\nconst generateTimeSeriesData = (baseValue: number, variance: number, points: number = 50) => {\n  const data = [];\n  const now = new Date();\n  \n  for (let i = points - 1; i >= 0; i--) {\n    const timestamp = new Date(now.getTime() - i * 30 * 60 * 1000); // 30 min intervals\n    const randomVariation = (Math.random() - 0.5) * variance;\n    const value = Math.max(0, baseValue + randomVariation);\n    \n    data.push({\n      timestamp: timestamp.toISOString(),\n      value: Math.round(value * 100) / 100\n    });\n  }\n  \n  return data;\n};\n\nexport const mockDashboardData: DashboardData = {\n  metrics: {\n    totalJobs: 869,\n    avgJobDuration: 190,\n    failureRate: 18.07,\n    successRate: 81.93\n  },\n  \n  jobDurationDistribution: [\n    { duration: '0-30s', count: 45, color: '#3b82f6' },\n    { duration: '30-60s', count: 120, color: '#3b82f6' },\n    { duration: '60-90s', count: 180, color: '#3b82f6' },\n    { duration: '90-120s', count: 220, color: '#3b82f6' },\n    { duration: '120-150s', count: 160, color: '#3b82f6' },\n    { duration: '150-180s', count: 90, color: '#3b82f6' },\n    { duration: '180-210s', count: 35, color: '#3b82f6' },\n    { duration: '210-240s', count: 15, color: '#3b82f6' },\n    { duration: '240s+', count: 4, color: '#3b82f6' }\n  ],\n  \n  charts: {\n    jobsRuns: generateTimeSeriesData(45, 20),\n    failureWave: generateTimeSeriesData(8, 6),\n    p95Duration: generateTimeSeriesData(180, 40),\n    p99Duration: generateTimeSeriesData(240, 60),\n    storagePerformance: generateTimeSeriesData(85, 15),\n    outputPerformance: generateTimeSeriesData(92, 12)\n  },\n  \n  performanceMetrics: [\n    { name: 'Memory', value: '2m 45s', percentage: '78%' },\n    { name: 'CPU', value: '1m 23s', percentage: '65%' },\n    { name: 'Disk', value: '45s', percentage: '34%' },\n    { name: 'Network', value: '1m 12s', percentage: '58%' },\n    { name: 'I/O', value: '2m 1s', percentage: '82%' }\n  ]\n};\n\nexport const navigationItems: NavItem[] = [\n  {\n    id: 'feedback',\n    label: 'FEEDBACK',\n    active: false\n  },\n  {\n    id: 'actions-analytics',\n    label: 'Actions Analytics',\n    active: true\n  },\n  {\n    id: 'workflow-runs',\n    label: 'Workflow Runs',\n    active: false\n  },\n  {\n    id: 'docker-builds',\n    label: 'Docker Builds',\n    active: false\n  },\n  {\n    id: 'cache',\n    label: 'Cache',\n    active: false\n  },\n  {\n    id: 'study-risks',\n    label: 'Study Risks',\n    active: false\n  },\n  {\n    id: 'usage-billing',\n    label: 'Usage & Billing',\n    active: false\n  },\n  {\n    id: 'settings',\n    label: 'Settings',\n    active: false\n  }\n];\n"], "names": [], "mappings": ";;;;AAEA,sCAAsC;AACtC,MAAM,yBAAyB,CAAC,WAAmB,UAAkB,SAAiB,EAAE;IACtF,MAAM,OAAO,EAAE;IACf,MAAM,MAAM,IAAI;IAEhB,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,IAAK;QACpC,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,OAAO,mBAAmB;QACnF,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAChD,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,YAAY;QAEtC,KAAK,IAAI,CAAC;YACR,WAAW,UAAU,WAAW;YAChC,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;QACnC;IACF;IAEA,OAAO;AACT;AAEO,MAAM,oBAAmC;IAC9C,SAAS;QACP,WAAW;QACX,gBAAgB;QAChB,aAAa;QACb,aAAa;IACf;IAEA,yBAAyB;QACvB;YAAE,UAAU;YAAS,OAAO;YAAI,OAAO;QAAU;QACjD;YAAE,UAAU;YAAU,OAAO;YAAK,OAAO;QAAU;QACnD;YAAE,UAAU;YAAU,OAAO;YAAK,OAAO;QAAU;QACnD;YAAE,UAAU;YAAW,OAAO;YAAK,OAAO;QAAU;QACpD;YAAE,UAAU;YAAY,OAAO;YAAK,OAAO;QAAU;QACrD;YAAE,UAAU;YAAY,OAAO;YAAI,OAAO;QAAU;QACpD;YAAE,UAAU;YAAY,OAAO;YAAI,OAAO;QAAU;QACpD;YAAE,UAAU;YAAY,OAAO;YAAI,OAAO;QAAU;QACpD;YAAE,UAAU;YAAS,OAAO;YAAG,OAAO;QAAU;KACjD;IAED,QAAQ;QACN,UAAU,uBAAuB,IAAI;QACrC,aAAa,uBAAuB,GAAG;QACvC,aAAa,uBAAuB,KAAK;QACzC,aAAa,uBAAuB,KAAK;QACzC,oBAAoB,uBAAuB,IAAI;QAC/C,mBAAmB,uBAAuB,IAAI;IAChD;IAEA,oBAAoB;QAClB;YAAE,MAAM;YAAU,OAAO;YAAU,YAAY;QAAM;QACrD;YAAE,MAAM;YAAO,OAAO;YAAU,YAAY;QAAM;QAClD;YAAE,MAAM;YAAQ,OAAO;YAAO,YAAY;QAAM;QAChD;YAAE,MAAM;YAAW,OAAO;YAAU,YAAY;QAAM;QACtD;YAAE,MAAM;YAAO,OAAO;YAAS,YAAY;QAAM;KAClD;AACH;AAEO,MAAM,kBAA6B;IACxC;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/src/app/page.tsx"], "sourcesContent": ["import { Dashboard } from \"@/components/Dashboard\";\nimport { mockDashboardData, navigationItems } from \"@/data/mockData\";\n\nexport default function Home() {\n  return (\n    <Dashboard data={mockDashboardData} navigationItems={navigationItems} />\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,+HAAA,CAAA,YAAS;QAAC,MAAM,uHAAA,CAAA,oBAAiB;QAAE,iBAAiB,uHAAA,CAAA,kBAAe;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/personal/blacksmith/blacksmith-app-augment/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}