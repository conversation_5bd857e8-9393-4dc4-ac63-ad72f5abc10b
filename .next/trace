[{"name": "hot-reloader", "duration": 181, "timestamp": 210603164683, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748349527636, "traceId": "b8db3db39361ccf2"}, {"name": "setup-dev-bundler", "duration": 520185, "timestamp": 210603009906, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748349527481, "traceId": "b8db3db39361ccf2"}, {"name": "run-instrumentation-hook", "duration": 14, "timestamp": 210603653356, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748349528125, "traceId": "b8db3db39361ccf2"}, {"name": "start-dev-server", "duration": 1733973, "timestamp": 210601927961, "id": 1, "tags": {"cpus": "10", "platform": "darwin", "memory.freeMem": "185925632", "memory.totalMem": "34359738368", "memory.heapSizeLimit": "17230200832", "memory.rss": "292405248", "memory.heapTotal": "99352576", "memory.heapUsed": "72447152"}, "startTime": 1748349526399, "traceId": "b8db3db39361ccf2"}, {"name": "compile-path", "duration": 2712928, "timestamp": 210621051529, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748349545523, "traceId": "b8db3db39361ccf2"}, {"name": "ensure-page", "duration": 2714211, "timestamp": 210621050636, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748349545522, "traceId": "b8db3db39361ccf2"}]